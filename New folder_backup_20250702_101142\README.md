# Attribution Dashboard

A Flask-based web application for tracking lead generation and sales performance across multiple channels including Google Ads, Meta Ads, and other sources.

## Features

- **Lead Report Dashboard**: Track lead generation performance across channels
- **Sales Report Dashboard**: Monitor sales performance and customer conversion
- **Google Ads Analytics**: Detailed Google Ads performance metrics
- **Meta Ads Analytics**: Facebook & Instagram advertising insights
- **Real-time Data**: Live data from Airtable integration
- **Interactive Charts**: Powered by Highcharts for rich visualizations

## Technology Stack

- **Backend**: Python Flask
- **Frontend**: HTML5, CSS3, JavaScript
- **Charts**: Highcharts
- **Data Source**: Airtable API
- **AI Integration**: Claude API for chat functionality

## Environment Variables

Required environment variables:

```
CLAUDE_API_KEY=your_claude_api_key_here
AIRTABLE_API_KEY=your_airtable_api_key_here
```

## Local Development

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Create `.env` file with your API keys

3. Run the application:
   ```bash
   python server.py
   ```

4. Open http://localhost:8000

## Deployment

This application is configured for deployment on Railway. The app will automatically:

- Use the PORT environment variable provided by Railway
- Bind to 0.0.0.0 for external access
- Include health check endpoint at `/health`
- Handle production logging

## API Endpoints

- `GET /health` - Health check
- `GET /api/airtable/records` - Fetch Airtable data
- `GET /api/latest-data-date` - Get latest data update date
- `POST /api/chat` - Claude AI chat integration
- `GET|DELETE /api/cache` - Cache management
