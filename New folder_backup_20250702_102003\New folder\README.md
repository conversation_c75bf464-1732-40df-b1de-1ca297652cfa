# Quickfix Attribution Dashboard

This is a customized version of the Attribution Dashboard specifically configured for Quickfix.

## Customization Details

### Business Information
- **Business Name**: Quickfix
- **Dashboard Title**: Attribution Dashboard
- **Contact Email**: <EMAIL>

### Branding
- **Primary Color**: #e91e63
- **Secondary Color**: #ff5722
- **Font Family**: Inter
- **Logo**: Default logo

### Location Configuration
- **Single Location Mode**: Yes
- **Location Filtering Enabled**: No
- **Locations**: Daphne, Mobile, Foley

### Airtable Configuration
- **Base ID**: Not configured

## Installation

1. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Configure environment variables:
   - Set your Airtable API key
   - Update the base ID in config.py if needed

3. Run the dashboard:
   ```bash
   python server.py
   ```

## Customization Notes

This dashboard has been customized using the Dashboard Customization Tool v1.0.

### Files Modified:
- `index.html` - Business name, logo, location filters
- `styles.css` - Colors, fonts, custom styles
- `script.js` - Location names, filtering logic
- `config.py` - Airtable configuration, client settings

### Custom CSS Applied:
```css
/* No custom CSS */
```

## Support

For support or questions about this customized dashboard, contact: <EMAIL>

Generated on: 2025-07-02 10:11:43
