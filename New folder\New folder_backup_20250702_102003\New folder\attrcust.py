#!/usr/bin/env python3
"""
Attribution Dashboard Customization Tool
A Python GUI application for analyzing, customizing, and deploying client-specific Attribution Dashboard instances.

Features:
- File analysis and validation
- Client-specific customization
- Location filtering management
- Code generation and deployment
- Branding and configuration management

Author: Attribution Dashboard Team
Version: 1.0.0
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import sys
import json
import re
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
import logging
from dataclasses import dataclass, asdict
import webbrowser

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('dashboard_customizer.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ClientConfig:
    """Client configuration data structure"""
    business_name: str = ""
    dashboard_title: str = ""
    logo_path: str = ""
    primary_color: str = "#e91e63"
    secondary_color: str = "#ff5722"
    font_family: str = "Inter"
    locations: List[str] = None
    enable_location_filtering: bool = True
    single_location_mode: bool = False
    custom_css: str = ""
    airtable_base_id: str = ""
    contact_email: str = ""
    
    def __post_init__(self):
        if self.locations is None:
            self.locations = ["Daphne", "Mobile", "Foley"]

@dataclass
class FileAnalysisResult:
    """Results from file analysis"""
    file_path: str
    exists: bool
    size: int
    location_references: List[str]
    customizable_elements: List[str]
    validation_errors: List[str]
    modification_suggestions: List[str]

class DashboardAnalyzer:
    """Analyzes the Attribution Dashboard codebase for customization opportunities"""
    
    def __init__(self):
        self.location_patterns = [
            r'\b(Daphne|Mobile|Foley)\b',
            r'location-filter',
            r'Location',
            r'locationData',
            r'locationBreakdown',
            r'Quick Fix - (Daphne|Mobile|Foley)'
        ]
        
        self.branding_patterns = [
            r'RepairLift',
            r'Attribution Dashboard',
            r'--primary:\s*#[a-fA-F0-9]{6}',
            r'--secondary:\s*#[a-fA-F0-9]{6}',
            r'logo-img',
            r'business_name'
        ]
        
        self.required_files = [
            'server.py',
            'index.html', 
            'script.js',
            'styles.css',
            'config.py',
            'requirements.txt'
        ]
    
    def analyze_directory(self, directory_path: str) -> Dict[str, FileAnalysisResult]:
        """Analyze the dashboard directory for customization opportunities"""
        results = {}
        
        for filename in self.required_files:
            file_path = os.path.join(directory_path, filename)
            results[filename] = self._analyze_file(file_path)
        
        return results
    
    def _analyze_file(self, file_path: str) -> FileAnalysisResult:
        """Analyze a single file for location dependencies and customization points"""
        result = FileAnalysisResult(
            file_path=file_path,
            exists=os.path.exists(file_path),
            size=0,
            location_references=[],
            customizable_elements=[],
            validation_errors=[],
            modification_suggestions=[]
        )
        
        if not result.exists:
            result.validation_errors.append(f"File not found: {file_path}")
            return result
        
        try:
            result.size = os.path.getsize(file_path)
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Find location references
            for pattern in self.location_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                result.location_references.extend(matches)
            
            # Find branding elements
            for pattern in self.branding_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                result.customizable_elements.extend(matches)
            
            # File-specific analysis
            if file_path.endswith('.html'):
                result = self._analyze_html_file(content, result)
            elif file_path.endswith('.js'):
                result = self._analyze_js_file(content, result)
            elif file_path.endswith('.css'):
                result = self._analyze_css_file(content, result)
            elif file_path.endswith('.py'):
                result = self._analyze_python_file(content, result)
                
        except Exception as e:
            result.validation_errors.append(f"Error analyzing file: {str(e)}")
        
        return result
    
    def _analyze_html_file(self, content: str, result: FileAnalysisResult) -> FileAnalysisResult:
        """Analyze HTML file for specific customization opportunities"""
        # Check for location filter dropdown
        if 'location-filter' in content:
            result.modification_suggestions.append("Location filter dropdown found - can be hidden for single-location clients")
        
        # Check for location-specific cards
        if 'location-stat' in content:
            result.modification_suggestions.append("Location-specific stat cards found - can be customized or hidden")
        
        # Check for logo references
        if 'logo-img' in content:
            result.modification_suggestions.append("Logo image found - can be replaced with client logo")
        
        # Check for hardcoded business names
        if 'RepairLift' in content or 'Quick Fix' in content:
            result.modification_suggestions.append("Hardcoded business names found - should be replaced with client name")
        
        return result
    
    def _analyze_js_file(self, content: str, result: FileAnalysisResult) -> FileAnalysisResult:
        """Analyze JavaScript file for location dependencies"""
        # Count location-related functions
        location_functions = re.findall(r'function.*[Ll]ocation.*\(', content)
        if location_functions:
            result.modification_suggestions.append(f"Found {len(location_functions)} location-related functions")
        
        # Check for location filtering logic
        if 'currentLocationFilter' in content:
            result.modification_suggestions.append("Location filtering logic found - can be disabled for single-location clients")
        
        # Check for location charts
        location_charts = re.findall(r'location.*Chart', content, re.IGNORECASE)
        if location_charts:
            result.modification_suggestions.append(f"Found {len(set(location_charts))} location-based charts")
        
        return result
    
    def _analyze_css_file(self, content: str, result: FileAnalysisResult) -> FileAnalysisResult:
        """Analyze CSS file for styling customization opportunities"""
        # Check for color variables
        color_vars = re.findall(r'--\w+:\s*#[a-fA-F0-9]{6}', content)
        if color_vars:
            result.modification_suggestions.append(f"Found {len(color_vars)} color variables that can be customized")
        
        # Check for location-specific styles
        if 'location-' in content:
            result.modification_suggestions.append("Location-specific CSS classes found")
        
        return result
    
    def _analyze_python_file(self, content: str, result: FileAnalysisResult) -> FileAnalysisResult:
        """Analyze Python file for configuration opportunities"""
        # Check for configuration classes
        if 'class.*Config' in content:
            result.modification_suggestions.append("Configuration classes found - can be customized for client")
        
        # Check for hardcoded values
        if 'AIRTABLE_BASE_URL' in content:
            result.modification_suggestions.append("Airtable configuration found - needs client-specific base ID")
        
        return result

class DashboardCustomizer:
    """Handles the customization and generation of client-specific dashboards"""
    
    def __init__(self):
        self.analyzer = DashboardAnalyzer()
    
    def customize_dashboard(self, source_dir: str, output_dir: str, config: ClientConfig) -> bool:
        """Generate a customized dashboard for a specific client"""
        try:
            # Create output directory
            os.makedirs(output_dir, exist_ok=True)
            
            # Copy all files first
            self._copy_dashboard_files(source_dir, output_dir)
            
            # Apply customizations
            self._customize_html_files(output_dir, config)
            self._customize_css_files(output_dir, config)
            self._customize_js_files(output_dir, config)
            self._customize_python_files(output_dir, config)
            
            # Generate client-specific configuration
            self._generate_client_config(output_dir, config)
            
            logger.info(f"Successfully customized dashboard for {config.business_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error customizing dashboard: {str(e)}")
            return False
    
    def _copy_dashboard_files(self, source_dir: str, output_dir: str):
        """Copy all dashboard files to output directory"""
        for item in os.listdir(source_dir):
            source_path = os.path.join(source_dir, item)
            dest_path = os.path.join(output_dir, item)
            
            if os.path.isfile(source_path):
                shutil.copy2(source_path, dest_path)
            elif os.path.isdir(source_path) and item not in ['.git', '__pycache__', 'logs']:
                shutil.copytree(source_path, dest_path, dirs_exist_ok=True)
    
    def _customize_html_files(self, output_dir: str, config: ClientConfig):
        """Customize HTML files with client-specific branding"""
        html_file = os.path.join(output_dir, 'index.html')
        if not os.path.exists(html_file):
            return
        
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace business name and title
        content = re.sub(r'RepairLift Attribution Logo', f'{config.business_name} Attribution Logo', content)
        content = re.sub(r'<title>.*?</title>', f'<title>{config.dashboard_title or config.business_name + " Attribution Dashboard"}</title>', content)
        
        # Replace logo if provided
        if config.logo_path:
            content = re.sub(r'src="img/rl\.svg"', f'src="{config.logo_path}"', content)
        
        # Handle location filtering for single-location clients
        if config.single_location_mode:
            # Hide location filter dropdown
            content = re.sub(
                r'<div class="filter-group">\s*<label for="location-filter">Location:</label>.*?</div>',
                '<!-- Location filter hidden for single-location client -->',
                content,
                flags=re.DOTALL
            )
            
            # Hide location-specific cards
            content = re.sub(
                r'<div class="location-stat".*?</div>\s*</div>',
                '<!-- Location stats hidden for single-location client -->',
                content,
                flags=re.DOTALL
            )
        
        # Replace hardcoded location names with client locations
        if config.locations:
            for i, location in enumerate(['Daphne', 'Mobile', 'Foley']):
                if i < len(config.locations):
                    content = content.replace(f'Quick Fix - {location}', f'{config.business_name} - {config.locations[i]}')
                    content = content.replace(f'>{location}<', f'>{config.locations[i]}<')
        
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def _customize_css_files(self, output_dir: str, config: ClientConfig):
        """Customize CSS files with client-specific styling"""
        css_file = os.path.join(output_dir, 'styles.css')
        if not os.path.exists(css_file):
            return
        
        with open(css_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Update color variables
        content = re.sub(r'--primary:\s*#[a-fA-F0-9]{6}', f'--primary: {config.primary_color}', content)
        content = re.sub(r'--secondary:\s*#[a-fA-F0-9]{6}', f'--secondary: {config.secondary_color}', content)
        
        # Update font family
        if config.font_family != "Inter":
            content = re.sub(r'font-family:\s*[^;]+', f'font-family: "{config.font_family}", sans-serif', content)
        
        # Add custom CSS if provided
        if config.custom_css:
            content += f"\n\n/* Client-specific custom styles */\n{config.custom_css}\n"
        
        # Hide location-specific elements for single-location clients
        if config.single_location_mode:
            content += """
/* Hide location-specific elements for single-location client */
.location-filter, .location-breakdown, .location-stat {
    display: none !important;
}
"""
        
        with open(css_file, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def _customize_js_files(self, output_dir: str, config: ClientConfig):
        """Customize JavaScript files for client-specific functionality"""
        js_file = os.path.join(output_dir, 'script.js')
        if not os.path.exists(js_file):
            return
        
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace hardcoded location names
        if config.locations:
            for i, location in enumerate(['Daphne', 'Mobile', 'Foley']):
                if i < len(config.locations):
                    content = content.replace(f"'{location}'", f"'{config.locations[i]}'")
                    content = content.replace(f'"{location}"', f'"{config.locations[i]}"')
                    content = content.replace(f'Quick Fix - {location}', f'{config.business_name} - {config.locations[i]}')
        
        # Disable location filtering for single-location clients
        if config.single_location_mode:
            # Set default location filter to the single location
            single_location = config.locations[0] if config.locations else "Main"
            content = re.sub(
                r"let currentLocationFilter = 'all';",
                f"let currentLocationFilter = '{single_location}';",
                content
            )
            
            # Disable location filter change events
            content += f"""
// Single-location mode: disable location filtering
document.addEventListener('DOMContentLoaded', function() {{
    const locationFilter = document.getElementById('location-filter');
    if (locationFilter) {{
        locationFilter.style.display = 'none';
        locationFilter.value = '{single_location}';
    }}
}});
"""
        
        with open(js_file, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def _customize_python_files(self, output_dir: str, config: ClientConfig):
        """Customize Python configuration files"""
        config_file = os.path.join(output_dir, 'config.py')
        if not os.path.exists(config_file):
            return
        
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Update Airtable base ID if provided
        if config.airtable_base_id:
            content = re.sub(
                r"AIRTABLE_BASE_ID = '[^']*'",
                f"AIRTABLE_BASE_ID = '{config.airtable_base_id}'",
                content
            )
        
        # Add client-specific configuration
        client_config_section = f"""
# Client-specific configuration for {config.business_name}
CLIENT_NAME = '{config.business_name}'
CLIENT_LOCATIONS = {config.locations}
SINGLE_LOCATION_MODE = {config.single_location_mode}
ENABLE_LOCATION_FILTERING = {config.enable_location_filtering}
"""
        
        content += client_config_section
        
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def _generate_client_config(self, output_dir: str, config: ClientConfig):
        """Generate a client-specific configuration file"""
        config_data = asdict(config)
        config_file = os.path.join(output_dir, 'client_config.json')
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2)

class DashboardCustomizerGUI:
    """Main GUI application for the Dashboard Customizer"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Attribution Dashboard Customizer v1.0")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)
        
        # Initialize components
        self.analyzer = DashboardAnalyzer()
        self.customizer = DashboardCustomizer()
        self.client_config = ClientConfig()
        self.source_directory = ""
        self.analysis_results = {}
        
        # Setup GUI
        self.setup_styles()
        self.create_widgets()
        self.setup_layout()
        
        # Load default values
        self.load_default_config()
    
    def setup_styles(self):
        """Configure ttk styles"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure custom styles
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
        style.configure('Heading.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Success.TLabel', foreground='green')
        style.configure('Error.TLabel', foreground='red')
        style.configure('Warning.TLabel', foreground='orange')
    
    def create_widgets(self):
        """Create all GUI widgets"""
        # Main notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        
        # Create tabs
        self.create_analysis_tab()
        self.create_customization_tab()
        self.create_generation_tab()
        self.create_logs_tab()
    
    def create_analysis_tab(self):
        """Create the file analysis tab"""
        self.analysis_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.analysis_frame, text="📁 File Analysis")
        
        # Source directory selection
        source_frame = ttk.LabelFrame(self.analysis_frame, text="Source Directory", padding=10)
        source_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(source_frame, text="Attribution Dashboard Source Directory:").pack(anchor='w')
        
        dir_frame = ttk.Frame(source_frame)
        dir_frame.pack(fill='x', pady=5)
        
        self.source_dir_var = tk.StringVar()
        self.source_dir_entry = ttk.Entry(dir_frame, textvariable=self.source_dir_var, width=80)
        self.source_dir_entry.pack(side='left', fill='x', expand=True)
        
        ttk.Button(dir_frame, text="Browse", command=self.browse_source_directory).pack(side='right', padx=(5, 0))
        
        # Analysis controls
        controls_frame = ttk.Frame(source_frame)
        controls_frame.pack(fill='x', pady=5)
        
        ttk.Button(controls_frame, text="🔍 Analyze Codebase", command=self.analyze_codebase).pack(side='left')
        ttk.Button(controls_frame, text="📋 Validate Structure", command=self.validate_structure).pack(side='left', padx=(5, 0))
        
        # Analysis results
        results_frame = ttk.LabelFrame(self.analysis_frame, text="Analysis Results", padding=10)
        results_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Create treeview for results
        columns = ('File', 'Status', 'Size', 'Location Refs', 'Customizable', 'Issues')
        self.analysis_tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.analysis_tree.heading(col, text=col)
            self.analysis_tree.column(col, width=120)
        
        # Scrollbars for treeview
        tree_scroll_y = ttk.Scrollbar(results_frame, orient='vertical', command=self.analysis_tree.yview)
        tree_scroll_x = ttk.Scrollbar(results_frame, orient='horizontal', command=self.analysis_tree.xview)
        self.analysis_tree.configure(yscrollcommand=tree_scroll_y.set, xscrollcommand=tree_scroll_x.set)
        
        self.analysis_tree.pack(side='left', fill='both', expand=True)
        tree_scroll_y.pack(side='right', fill='y')
        tree_scroll_x.pack(side='bottom', fill='x')
        
        # Details panel
        details_frame = ttk.LabelFrame(self.analysis_frame, text="File Details", padding=10)
        details_frame.pack(fill='x', padx=10, pady=5)
        
        self.details_text = scrolledtext.ScrolledText(details_frame, height=8, wrap='word')
        self.details_text.pack(fill='both', expand=True)
        
        # Bind selection event
        self.analysis_tree.bind('<<TreeviewSelect>>', self.on_file_select)
    
    def create_customization_tab(self):
        """Create the client customization tab"""
        self.customization_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.customization_frame, text="🎨 Client Customization")
        
        # Create scrollable frame
        canvas = tk.Canvas(self.customization_frame)
        scrollbar = ttk.Scrollbar(self.customization_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Basic Information
        basic_frame = ttk.LabelFrame(scrollable_frame, text="Basic Information", padding=10)
        basic_frame.pack(fill='x', padx=10, pady=5)
        
        # Business name
        ttk.Label(basic_frame, text="Business Name:").grid(row=0, column=0, sticky='w', pady=2)
        self.business_name_var = tk.StringVar()
        ttk.Entry(basic_frame, textvariable=self.business_name_var, width=40).grid(row=0, column=1, sticky='ew', padx=(5, 0), pady=2)
        
        # Dashboard title
        ttk.Label(basic_frame, text="Dashboard Title:").grid(row=1, column=0, sticky='w', pady=2)
        self.dashboard_title_var = tk.StringVar()
        ttk.Entry(basic_frame, textvariable=self.dashboard_title_var, width=40).grid(row=1, column=1, sticky='ew', padx=(5, 0), pady=2)
        
        # Contact email
        ttk.Label(basic_frame, text="Contact Email:").grid(row=2, column=0, sticky='w', pady=2)
        self.contact_email_var = tk.StringVar()
        ttk.Entry(basic_frame, textvariable=self.contact_email_var, width=40).grid(row=2, column=1, sticky='ew', padx=(5, 0), pady=2)
        
        basic_frame.columnconfigure(1, weight=1)
        
        # Branding
        branding_frame = ttk.LabelFrame(scrollable_frame, text="Branding & Styling", padding=10)
        branding_frame.pack(fill='x', padx=10, pady=5)
        
        # Logo path
        ttk.Label(branding_frame, text="Logo Path:").grid(row=0, column=0, sticky='w', pady=2)
        logo_frame = ttk.Frame(branding_frame)
        logo_frame.grid(row=0, column=1, sticky='ew', padx=(5, 0), pady=2)
        
        self.logo_path_var = tk.StringVar()
        ttk.Entry(logo_frame, textvariable=self.logo_path_var).pack(side='left', fill='x', expand=True)
        ttk.Button(logo_frame, text="Browse", command=self.browse_logo).pack(side='right', padx=(5, 0))
        
        # Colors
        ttk.Label(branding_frame, text="Primary Color:").grid(row=1, column=0, sticky='w', pady=2)
        self.primary_color_var = tk.StringVar(value="#e91e63")
        color_frame1 = ttk.Frame(branding_frame)
        color_frame1.grid(row=1, column=1, sticky='ew', padx=(5, 0), pady=2)
        ttk.Entry(color_frame1, textvariable=self.primary_color_var, width=10).pack(side='left')
        self.primary_color_preview = tk.Label(color_frame1, width=3, bg=self.primary_color_var.get())
        self.primary_color_preview.pack(side='left', padx=(5, 0))
        
        ttk.Label(branding_frame, text="Secondary Color:").grid(row=2, column=0, sticky='w', pady=2)
        self.secondary_color_var = tk.StringVar(value="#ff5722")
        color_frame2 = ttk.Frame(branding_frame)
        color_frame2.grid(row=2, column=1, sticky='ew', padx=(5, 0), pady=2)
        ttk.Entry(color_frame2, textvariable=self.secondary_color_var, width=10).pack(side='left')
        self.secondary_color_preview = tk.Label(color_frame2, width=3, bg=self.secondary_color_var.get())
        self.secondary_color_preview.pack(side='left', padx=(5, 0))
        
        # Font family
        ttk.Label(branding_frame, text="Font Family:").grid(row=3, column=0, sticky='w', pady=2)
        self.font_family_var = tk.StringVar(value="Inter")
        font_combo = ttk.Combobox(branding_frame, textvariable=self.font_family_var, 
                                 values=["Inter", "Arial", "Helvetica", "Roboto", "Open Sans", "Lato"])
        font_combo.grid(row=3, column=1, sticky='ew', padx=(5, 0), pady=2)
        
        branding_frame.columnconfigure(1, weight=1)
        
        # Location Settings
        location_frame = ttk.LabelFrame(scrollable_frame, text="Location Settings", padding=10)
        location_frame.pack(fill='x', padx=10, pady=5)
        
        # Single location mode
        self.single_location_var = tk.BooleanVar()
        ttk.Checkbutton(location_frame, text="Single Location Mode (Hide location filtering)", 
                       variable=self.single_location_var, command=self.on_single_location_change).pack(anchor='w')
        
        # Enable location filtering
        self.enable_location_filtering_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(location_frame, text="Enable Location Filtering", 
                       variable=self.enable_location_filtering_var).pack(anchor='w')
        
        # Locations list
        ttk.Label(location_frame, text="Locations (one per line):").pack(anchor='w', pady=(10, 0))
        self.locations_text = scrolledtext.ScrolledText(location_frame, height=4, width=50)
        self.locations_text.pack(fill='x', pady=5)
        self.locations_text.insert('1.0', "Daphne\nMobile\nFoley")
        
        # Airtable Configuration
        airtable_frame = ttk.LabelFrame(scrollable_frame, text="Airtable Configuration", padding=10)
        airtable_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(airtable_frame, text="Airtable Base ID:").grid(row=0, column=0, sticky='w', pady=2)
        self.airtable_base_var = tk.StringVar()
        ttk.Entry(airtable_frame, textvariable=self.airtable_base_var, width=40).grid(row=0, column=1, sticky='ew', padx=(5, 0), pady=2)
        
        airtable_frame.columnconfigure(1, weight=1)
        
        # Custom CSS
        css_frame = ttk.LabelFrame(scrollable_frame, text="Custom CSS", padding=10)
        css_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        ttk.Label(css_frame, text="Additional CSS styles:").pack(anchor='w')
        self.custom_css_text = scrolledtext.ScrolledText(css_frame, height=6)
        self.custom_css_text.pack(fill='both', expand=True, pady=5)
        
        # Action buttons
        action_frame = ttk.Frame(scrollable_frame)
        action_frame.pack(fill='x', padx=10, pady=10)
        
        ttk.Button(action_frame, text="💾 Save Configuration", command=self.save_config).pack(side='left')
        ttk.Button(action_frame, text="📂 Load Configuration", command=self.load_config).pack(side='left', padx=(5, 0))
        ttk.Button(action_frame, text="🔄 Reset to Defaults", command=self.reset_config).pack(side='left', padx=(5, 0))
        ttk.Button(action_frame, text="👁️ Preview Changes", command=self.preview_changes).pack(side='right')
        
        # Pack scrollable frame
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Bind color change events
        self.primary_color_var.trace('w', self.update_color_preview)
        self.secondary_color_var.trace('w', self.update_color_preview)

    def get_client_config(self):
        """Get current client configuration from GUI"""
        locations = [loc.strip() for loc in self.locations_text.get('1.0', tk.END).strip().split('\n') if loc.strip()]
        
        return ClientConfig(
            business_name=self.business_name_var.get(),
            dashboard_title=self.dashboard_title_var.get(),
            logo_path=self.logo_path_var.get(),
            primary_color=self.primary_color_var.get(),
            secondary_color=self.secondary_color_var.get(),
            font_family=self.font_family_var.get(),
            locations=locations,
            enable_location_filtering=self.enable_location_filtering_var.get(),
            single_location_mode=self.single_location_var.get(),
            custom_css=self.custom_css_text.get('1.0', tk.END).strip(),
            airtable_base_id=self.airtable_base_var.get(),
            contact_email=self.contact_email_var.get()
        )
       
    def set_client_config(self, config: ClientConfig):
        """Set GUI values from client configuration"""
        self.business_name_var.set(config.business_name)
        self.dashboard_title_var.set(config.dashboard_title)
        self.logo_path_var.set(config.logo_path)
        self.primary_color_var.set(config.primary_color)
        self.secondary_color_var.set(config.secondary_color)
        self.font_family_var.set(config.font_family)
        self.enable_location_filtering_var.set(config.enable_location_filtering)
        self.single_location_var.set(config.single_location_mode)
        self.airtable_base_var.set(config.airtable_base_id)
        self.contact_email_var.set(config.contact_email)
        
        # Set locations
        self.locations_text.delete('1.0', tk.END)
        if config.locations:
            self.locations_text.insert('1.0', '\n'.join(config.locations))
        
        # Set custom CSS
        self.custom_css_text.delete('1.0', tk.END)
        if config.custom_css:
            self.custom_css_text.insert('1.0', config.custom_css)
        
        self.update_color_preview()
    
    def save_config(self):
        """Save current configuration to file"""
        config = self.get_client_config()
        
        file_path = filedialog.asksaveasfilename(
            title="Save Client Configuration",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(asdict(config), f, indent=2)
                
                messagebox.showinfo("Success", f"Configuration saved to {file_path}")
                self.status_var.set(f"Configuration saved: {file_path}")
                logger.info(f"Configuration saved to {file_path}")
                
            except Exception as e:
                error_msg = f"Error saving configuration: {str(e)}"
                messagebox.showerror("Save Error", error_msg)
                logger.error(error_msg)
    
    def load_config(self):
        """Load configuration from file"""
        file_path = filedialog.askopenfilename(
            title="Load Client Configuration",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                config = ClientConfig(**config_data)
                self.set_client_config(config)
                
                messagebox.showinfo("Success", f"Configuration loaded from {file_path}")
                self.status_var.set(f"Configuration loaded: {file_path}")
                logger.info(f"Configuration loaded from {file_path}")
                
            except Exception as e:
                error_msg = f"Error loading configuration: {str(e)}"
                messagebox.showerror("Load Error", error_msg)
                logger.error(error_msg)
    
    def reset_config(self):
        """Reset configuration to defaults"""
        if messagebox.askyesno("Reset Configuration", "Are you sure you want to reset all settings to defaults?"):
            self.load_default_config()
            self.locations_text.delete('1.0', tk.END)
            self.locations_text.insert('1.0', "Daphne\nMobile\nFoley")
            self.custom_css_text.delete('1.0', tk.END)
            self.single_location_var.set(False)
            self.enable_location_filtering_var.set(True)
            self.airtable_base_var.set("")
            self.status_var.set("Configuration reset to defaults")
    
    def preview_changes(self):
        """Preview the changes that will be made"""
        config = self.get_client_config()
        
        preview_window = tk.Toplevel(self.root)
        preview_window.title("Preview Changes")
        preview_window.geometry("800x600")
        
        # Create preview content
        preview_text = scrolledtext.ScrolledText(preview_window, wrap='word', font=('Consolas', 10))
        preview_text.pack(fill='both', expand=True, padx=10, pady=10)
        
        preview_content = f"""
DASHBOARD CUSTOMIZATION PREVIEW
{'=' * 50}

Business Information:
• Business Name: {config.business_name}
• Dashboard Title: {config.dashboard_title}
• Contact Email: {config.contact_email}

Branding Changes:
• Logo: {config.logo_path or 'Default logo will be used'}
• Primary Color: {config.primary_color}
• Secondary Color: {config.secondary_color}
• Font Family: {config.font_family}

Location Settings:
• Single Location Mode: {'Yes' if config.single_location_mode else 'No'}
• Enable Location Filtering: {'Yes' if config.enable_location_filtering else 'No'}
• Locations: {', '.join(config.locations) if config.locations else 'None specified'}

Airtable Configuration:
• Base ID: {config.airtable_base_id or 'Not specified'}

Files that will be modified:
• index.html - Business name, logo, location filters
• styles.css - Colors, fonts, custom styles
• script.js - Location names, filtering logic
• config.py - Airtable configuration, client settings

Custom CSS:
{config.custom_css or 'No custom CSS specified'}

Location-specific modifications:
"""
        
        if config.single_location_mode:
            preview_content += """
• Location filter dropdown will be hidden
• Location comparison features will be disabled
• Location-specific charts will show single location only
• Location breakdown cards will be hidden
"""
        else:
            preview_content += """
• All location features will remain active
• Location filtering will be available
• Multi-location charts and comparisons enabled
"""
        
        preview_text.insert('1.0', preview_content)
        preview_text.config(state='disabled')
        
        # Add close button
        ttk.Button(preview_window, text="Close", command=preview_window.destroy).pack(pady=10)
    
    def generate_dashboard(self):
        """Generate the customized dashboard"""
        if not self.source_directory:
            messagebox.showerror("Error", "Please select a source directory first.")
            return
        
        if not self.output_dir_var.get():
            messagebox.showerror("Error", "Please select an output directory.")
            return
        
        config = self.get_client_config()
        
        if not config.business_name:
            messagebox.showerror("Error", "Please enter a business name.")
            return
        
        # Confirm generation
        if not messagebox.askyesno("Generate Dashboard", 
                                  f"Generate customized dashboard for {config.business_name}?\n\n"
                                  f"Source: {self.source_directory}\n"
                                  f"Output: {self.output_dir_var.get()}"):
            return
        
        # Start generation process
        self.progress_var.set("Starting generation...")
        self.progress_bar['value'] = 0
        self.root.update()
        
        try:
            # Clear previous results
            self.generation_results.delete('1.0', tk.END)
            
            # Step 1: Validate source
            self.progress_var.set("Validating source directory...")
            self.progress_bar['value'] = 10
            self.root.update()
            
            if not self.analysis_results:
                self.analyze_codebase()
            
            # Step 2: Create backup if requested
            if self.backup_original_var.get():
                self.progress_var.set("Creating backup...")
                self.progress_bar['value'] = 20
                self.root.update()
                
                backup_dir = self.output_dir_var.get() + "_backup_" + datetime.now().strftime("%Y%m%d_%H%M%S")
                shutil.copytree(self.source_directory, backup_dir)
                self.log_generation(f"✅ Backup created: {backup_dir}")
            
            # Step 3: Generate customized dashboard
            self.progress_var.set("Generating customized dashboard...")
            self.progress_bar['value'] = 40
            self.root.update()
            
            success = self.customizer.customize_dashboard(
                self.source_directory,
                self.output_dir_var.get(),
                config
            )
            
            if not success:
                raise Exception("Dashboard customization failed")
            
            self.log_generation(f"✅ Dashboard customized successfully")
            
            # Step 4: Validate output if requested
            if self.validate_output_var.get():
                self.progress_var.set("Validating generated files...")
                self.progress_bar['value'] = 70
                self.root.update()
                
                self.validate_generated_files(self.output_dir_var.get())
            
            # Step 5: Generate README if requested
            if self.generate_readme_var.get():
                self.progress_var.set("Generating documentation...")
                self.progress_bar['value'] = 85
                self.root.update()
                
                self.generate_readme_file(self.output_dir_var.get(), config)
            
            # Step 6: Complete
            self.progress_var.set("Generation complete!")
            self.progress_bar['value'] = 100
            self.root.update()
            
            self.log_generation(f"🎉 Dashboard generation completed successfully!")
            self.log_generation(f"📁 Output directory: {self.output_dir_var.get()}")
            
            # Show completion message
            messagebox.showinfo("Success", 
                              f"Dashboard generated successfully!\n\n"
                              f"Output: {self.output_dir_var.get()}\n"
                              f"Business: {config.business_name}")
            
            self.status_var.set(f"Dashboard generated for {config.business_name}")
            
        except Exception as e:
            error_msg = f"Error generating dashboard: {str(e)}"
            self.log_generation(f"❌ {error_msg}")
            messagebox.showerror("Generation Error", error_msg)
            self.progress_var.set("Generation failed")
            logger.error(error_msg)
    
    def preview_generation(self):
        """Preview what will be generated without actually creating files"""
        if not self.source_directory:
            messagebox.showerror("Error", "Please select a source directory first.")
            return
        
        config = self.get_client_config()
        
        # Create preview window
        preview_window = tk.Toplevel(self.root)
        preview_window.title("Generation Preview")
        preview_window.geometry("900x700")
        
        # Create notebook for different preview tabs
        preview_notebook = ttk.Notebook(preview_window)
        preview_notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # HTML Preview
        html_frame = ttk.Frame(preview_notebook)
        preview_notebook.add(html_frame, text="HTML Changes")
        
        html_text = scrolledtext.ScrolledText(html_frame, wrap='word', font=('Consolas', 9))
        html_text.pack(fill='both', expand=True)
        
        # CSS Preview
        css_frame = ttk.Frame(preview_notebook)
        preview_notebook.add(css_frame, text="CSS Changes")
        
        css_text = scrolledtext.ScrolledText(css_frame, wrap='word', font=('Consolas', 9))
        css_text.pack(fill='both', expand=True)
        
        # JavaScript Preview
        js_frame = ttk.Frame(preview_notebook)
        preview_notebook.add(js_frame, text="JavaScript Changes")
        
        js_text = scrolledtext.ScrolledText(js_frame, wrap='word', font=('Consolas', 9))
        js_text.pack(fill='both', expand=True)
        
        # Generate preview content
        self.generate_preview_content(html_text, css_text, js_text, config)
        
        # Add close button
        ttk.Button(preview_window, text="Close", command=preview_window.destroy).pack(pady=10)
    
    def generate_preview_content(self, html_text, css_text, js_text, config):
        """Generate preview content for different file types"""
        # HTML Preview
        html_preview = f"""
HTML MODIFICATIONS PREVIEW
{'=' * 30}

Title Changes:
• Page title: "{config.dashboard_title or config.business_name + ' Attribution Dashboard'}"
• Logo alt text: "{config.business_name} Attribution Logo"

Logo Changes:
• Logo source: {config.logo_path or 'img/rl.svg (default)'}

Business Name Replacements:
• "RepairLift" → "{config.business_name}"
• "Quick Fix" → "{config.business_name}"

Location Filter Changes:
"""
        
        if config.single_location_mode:
            html_preview += "• Location filter dropdown will be HIDDEN\n"
            html_preview += "• Location-specific cards will be HIDDEN\n"
        else:
            html_preview += "• Location filter dropdown will remain visible\n"
            if config.locations:
                html_preview += f"• Location options: {', '.join(config.locations)}\n"
        
        html_text.insert('1.0', html_preview)
        
        # CSS Preview
        css_preview = f"""
CSS MODIFICATIONS PREVIEW
{'=' * 30}

Color Variables:
• --primary: {config.primary_color}
• --secondary: {config.secondary_color}

Font Family:
• Primary font: "{config.font_family}", sans-serif

Custom CSS:
{config.custom_css or 'No custom CSS specified'}

Single Location Mode Styles:
"""
        
        if config.single_location_mode:
            css_preview += """
.location-filter, .location-breakdown, .location-stat {
    display: none !important;
}
"""
        else:
            css_preview += "No single-location mode styles will be added"
        
        css_text.insert('1.0', css_preview)
        
        # JavaScript Preview
        js_preview = f"""
JAVASCRIPT MODIFICATIONS PREVIEW
{'=' * 30}

Location Name Replacements:
"""
        
        if config.locations:
            default_locations = ['Daphne', 'Mobile', 'Foley']
            for i, location in enumerate(default_locations):
                if i < len(config.locations):
                    js_preview += f"• '{location}' → '{config.locations[i]}'\n"
                    js_preview += f"• 'Quick Fix - {location}' → '{config.business_name} - {config.locations[i]}'\n"
        
        if config.single_location_mode:
            single_location = config.locations[0] if config.locations else "Main"
            js_preview += f"""
Single Location Mode Changes:
• currentLocationFilter set to: '{single_location}'
• Location filter dropdown will be hidden
• Location filtering events will be disabled
"""
        
        js_text.insert('1.0', js_preview)
    
    def validate_generated_files(self, output_dir):
        """Validate the generated files"""
        validation_results = []
        
        for filename in self.analyzer.required_files:
            file_path = os.path.join(output_dir, filename)
            if os.path.exists(file_path):
                validation_results.append(f"✅ {filename} - Generated successfully")
            else:
                validation_results.append(f"❌ {filename} - Missing")
        
        # Check for client config file
        client_config_path = os.path.join(output_dir, 'client_config.json')
        if os.path.exists(client_config_path):
            validation_results.append("✅ client_config.json - Generated successfully")
        
        for result in validation_results:
            self.log_generation(result)
    
    def generate_readme_file(self, output_dir, config):
        """Generate README file with customization details"""
        readme_content = f"""# {config.business_name} Attribution Dashboard

This is a customized version of the Attribution Dashboard specifically configured for {config.business_name}.

## Customization Details

### Business Information
- **Business Name**: {config.business_name}
- **Dashboard Title**: {config.dashboard_title}
- **Contact Email**: {config.contact_email}

### Branding
- **Primary Color**: {config.primary_color}
- **Secondary Color**: {config.secondary_color}
- **Font Family**: {config.font_family}
- **Logo**: {config.logo_path or 'Default logo'}

### Location Configuration
- **Single Location Mode**: {'Yes' if config.single_location_mode else 'No'}
- **Location Filtering Enabled**: {'Yes' if config.enable_location_filtering else 'No'}
- **Locations**: {', '.join(config.locations) if config.locations else 'Default locations'}

### Airtable Configuration
- **Base ID**: {config.airtable_base_id or 'Not configured'}

## Installation

1. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Configure environment variables:
   - Set your Airtable API key
   - Update the base ID in config.py if needed

3. Run the dashboard:
   ```bash
   python server.py
   ```

## Customization Notes

This dashboard has been customized using the Dashboard Customization Tool v1.0.

### Files Modified:
- `index.html` - Business name, logo, location filters
- `styles.css` - Colors, fonts, custom styles
- `script.js` - Location names, filtering logic
- `config.py` - Airtable configuration, client settings

### Custom CSS Applied:
```css
{config.custom_css or '/* No custom CSS */'}
```

## Support

For support or questions about this customized dashboard, contact: {config.contact_email}

Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

        readme_path = os.path.join(output_dir, 'README.md')
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)

        self.log_generation(f"✅ README.md generated")

    def log_generation(self, message):
        """Log a message to the generation results"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_message = f"[{timestamp}] {message}\n"
        self.generation_results.insert(tk.END, log_message)
        self.generation_results.see(tk.END)
        self.root.update()

    def create_generation_tab(self):
        """Create the code generation tab"""
        self.generation_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.generation_frame, text="🚀 Generate Dashboard")

        # Output directory selection
        output_frame = ttk.LabelFrame(self.generation_frame, text="Output Configuration", padding=10)
        output_frame.pack(fill='x', padx=10, pady=5)

        ttk.Label(output_frame, text="Output Directory:").pack(anchor='w')

        output_dir_frame = ttk.Frame(output_frame)
        output_dir_frame.pack(fill='x', pady=5)

        self.output_dir_var = tk.StringVar()
        ttk.Entry(output_dir_frame, textvariable=self.output_dir_var, width=80).pack(side='left', fill='x', expand=True)
        ttk.Button(output_dir_frame, text="Browse", command=self.browse_output_directory).pack(side='right', padx=(5, 0))

        # Generation options
        options_frame = ttk.LabelFrame(self.generation_frame, text="Generation Options", padding=10)
        options_frame.pack(fill='x', padx=10, pady=5)

        self.backup_original_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Create backup of original files",
                       variable=self.backup_original_var).pack(anchor='w')

        self.validate_output_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Validate generated files",
                       variable=self.validate_output_var).pack(anchor='w')

        self.generate_readme_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Generate README with customization details",
                       variable=self.generate_readme_var).pack(anchor='w')

        # Generation controls
        controls_frame = ttk.Frame(options_frame)
        controls_frame.pack(fill='x', pady=10)

        ttk.Button(controls_frame, text="🎯 Generate Customized Dashboard",
                  command=self.generate_dashboard).pack(side='left')
        ttk.Button(controls_frame, text="🔍 Preview Changes Only",
                  command=self.preview_generation).pack(side='left', padx=(10, 0))

        # Progress and status
        progress_frame = ttk.LabelFrame(self.generation_frame, text="Generation Progress", padding=10)
        progress_frame.pack(fill='x', padx=10, pady=5)

        self.progress_var = tk.StringVar(value="Ready to generate...")
        ttk.Label(progress_frame, textvariable=self.progress_var).pack(anchor='w')

        self.progress_bar = ttk.Progressbar(progress_frame, mode='determinate')
        self.progress_bar.pack(fill='x', pady=5)

        # Generation results
        results_frame = ttk.LabelFrame(self.generation_frame, text="Generation Results", padding=10)
        results_frame.pack(fill='both', expand=True, padx=10, pady=5)

        self.generation_results = scrolledtext.ScrolledText(results_frame, height=15, wrap='word')
        self.generation_results.pack(fill='both', expand=True)

        # Action buttons
        action_frame = ttk.Frame(results_frame)
        action_frame.pack(fill='x', pady=(10, 0))

        ttk.Button(action_frame, text="📂 Open Output Directory",
                  command=self.open_output_directory).pack(side='left')
        ttk.Button(action_frame, text="🌐 Test Generated Dashboard",
                  command=self.test_dashboard).pack(side='left', padx=(5, 0))
        ttk.Button(action_frame, text="📋 Copy Generation Report",
                  command=self.copy_generation_report).pack(side='right')

    def create_logs_tab(self):
        """Create the logs and debugging tab"""
        self.logs_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.logs_frame, text="📋 Logs & Debug")

        # Log controls
        controls_frame = ttk.Frame(self.logs_frame)
        controls_frame.pack(fill='x', padx=10, pady=5)

        ttk.Button(controls_frame, text="🔄 Refresh Logs", command=self.refresh_logs).pack(side='left')
        ttk.Button(controls_frame, text="🗑️ Clear Logs", command=self.clear_logs).pack(side='left', padx=(5, 0))
        ttk.Button(controls_frame, text="💾 Save Logs", command=self.save_logs).pack(side='left', padx=(5, 0))

        # Log level selection
        ttk.Label(controls_frame, text="Log Level:").pack(side='right', padx=(0, 5))
        self.log_level_var = tk.StringVar(value="INFO")
        log_level_combo = ttk.Combobox(controls_frame, textvariable=self.log_level_var,
                                      values=["DEBUG", "INFO", "WARNING", "ERROR"], width=10)
        log_level_combo.pack(side='right')
        log_level_combo.bind('<<ComboboxSelected>>', self.change_log_level)

        # Log display
        log_frame = ttk.LabelFrame(self.logs_frame, text="Application Logs", padding=10)
        log_frame.pack(fill='both', expand=True, padx=10, pady=5)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=20, wrap='word', font=('Consolas', 9))
        self.log_text.pack(fill='both', expand=True)

        # Debug information
        debug_frame = ttk.LabelFrame(self.logs_frame, text="Debug Information", padding=10)
        debug_frame.pack(fill='x', padx=10, pady=5)

        debug_info = f"""
Application Version: 1.0.0
Python Version: {sys.version}
Platform: {sys.platform}
Working Directory: {os.getcwd()}
Log File: dashboard_customizer.log
        """.strip()

        ttk.Label(debug_frame, text=debug_info, font=('Consolas', 9)).pack(anchor='w')

    def setup_layout(self):
        """Setup the main layout"""
        self.notebook.pack(fill='both', expand=True, padx=5, pady=5)

        # Status bar
        self.status_frame = ttk.Frame(self.root)
        self.status_frame.pack(fill='x', side='bottom')

        self.status_var = tk.StringVar(value="Ready - Select source directory to begin analysis")
        ttk.Label(self.status_frame, textvariable=self.status_var).pack(side='left', padx=5, pady=2)

        # Version info
        ttk.Label(self.status_frame, text="Dashboard Customizer v1.0",
                 font=('Arial', 8)).pack(side='right', padx=5, pady=2)

    def load_default_config(self):
        """Load default configuration values"""
        self.business_name_var.set("Your Business Name")
        self.dashboard_title_var.set("Attribution Dashboard")
        self.contact_email_var.set("<EMAIL>")
        self.primary_color_var.set("#e91e63")
        self.secondary_color_var.set("#ff5722")
        self.font_family_var.set("Inter")
        self.update_color_preview()

    # Event handlers and utility methods
    def browse_source_directory(self):
        """Browse for source directory"""
        directory = filedialog.askdirectory(title="Select Attribution Dashboard Source Directory")
        if directory:
            self.source_dir_var.set(directory)
            self.source_directory = directory
            self.status_var.set(f"Source directory selected: {directory}")

    def browse_output_directory(self):
        """Browse for output directory"""
        directory = filedialog.askdirectory(title="Select Output Directory for Customized Dashboard")
        if directory:
            self.output_dir_var.set(directory)
            self.status_var.set(f"Output directory selected: {directory}")

    def browse_logo(self):
        """Browse for logo file"""
        file_path = filedialog.askopenfilename(
            title="Select Logo File",
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.gif *.svg"), ("All files", "*.*")]
        )
        if file_path:
            self.logo_path_var.set(file_path)

    def update_color_preview(self, *args):
        """Update color preview labels"""
        try:
            self.primary_color_preview.config(bg=self.primary_color_var.get())
        except tk.TclError:
            self.primary_color_preview.config(bg='white')

        try:
            self.secondary_color_preview.config(bg=self.secondary_color_var.get())
        except tk.TclError:
            self.secondary_color_preview.config(bg='white')

    def on_single_location_change(self):
        """Handle single location mode change"""
        if self.single_location_var.get():
            self.enable_location_filtering_var.set(False)
            messagebox.showinfo("Single Location Mode",
                              "Location filtering will be disabled and location-specific UI elements will be hidden.")
        else:
            self.enable_location_filtering_var.set(True)

    def analyze_codebase(self):
        """Analyze the source codebase"""
        if not self.source_directory:
            messagebox.showerror("Error", "Please select a source directory first.")
            return

        self.status_var.set("Analyzing codebase...")
        self.root.update()

        try:
            # Clear previous results
            for item in self.analysis_tree.get_children():
                self.analysis_tree.delete(item)

            # Perform analysis
            self.analysis_results = self.analyzer.analyze_directory(self.source_directory)

            # Populate results tree
            for filename, result in self.analysis_results.items():
                status = "✅ Found" if result.exists else "❌ Missing"
                size = f"{result.size:,} bytes" if result.exists else "N/A"
                location_refs = len(set(result.location_references))
                customizable = len(set(result.customizable_elements))
                issues = len(result.validation_errors)

                item_id = self.analysis_tree.insert('', 'end', values=(
                    filename, status, size, location_refs, customizable, issues
                ))

                # Color code based on status
                if not result.exists:
                    self.analysis_tree.set(item_id, 'Status', '❌ Missing')
                elif result.validation_errors:
                    self.analysis_tree.set(item_id, 'Status', '⚠️ Issues')
                else:
                    self.analysis_tree.set(item_id, 'Status', '✅ OK')

            self.status_var.set(f"Analysis complete - {len(self.analysis_results)} files analyzed")
            logger.info(f"Codebase analysis completed for {self.source_directory}")

        except Exception as e:
            error_msg = f"Error analyzing codebase: {str(e)}"
            messagebox.showerror("Analysis Error", error_msg)
            self.status_var.set("Analysis failed")
            logger.error(error_msg)

    def validate_structure(self):
        """Validate the dashboard structure"""
        if not self.analysis_results:
            messagebox.showwarning("Warning", "Please run codebase analysis first.")
            return

        missing_files = []
        files_with_errors = []

        for filename, result in self.analysis_results.items():
            if not result.exists:
                missing_files.append(filename)
            elif result.validation_errors:
                files_with_errors.append(filename)

        if missing_files or files_with_errors:
            message = "Validation Issues Found:\n\n"
            if missing_files:
                message += f"Missing Files:\n" + "\n".join(f"• {f}" for f in missing_files) + "\n\n"
            if files_with_errors:
                message += f"Files with Errors:\n" + "\n".join(f"• {f}" for f in files_with_errors)

            messagebox.showwarning("Validation Issues", message)
        else:
            messagebox.showinfo("Validation Success", "All required files found and validated successfully!")

    def on_file_select(self, event):
        """Handle file selection in analysis tree"""
        selection = self.analysis_tree.selection()
        if not selection:
            return

        item = selection[0]
        filename = self.analysis_tree.item(item, 'values')[0]

        if filename in self.analysis_results:
            result = self.analysis_results[filename]

            details = f"File: {filename}\n"
            details += f"Path: {result.file_path}\n"
            details += f"Exists: {'Yes' if result.exists else 'No'}\n"
            details += f"Size: {result.size:,} bytes\n\n"

            if result.location_references:
                details += f"Location References ({len(result.location_references)}):\n"
                for ref in sorted(set(result.location_references)):
                    details += f"• {ref}\n"
                details += "\n"

            if result.customizable_elements:
                details += f"Customizable Elements ({len(result.customizable_elements)}):\n"
                for elem in sorted(set(result.customizable_elements)):
                    details += f"• {elem}\n"
                details += "\n"

            if result.modification_suggestions:
                details += "Modification Suggestions:\n"
                for suggestion in result.modification_suggestions:
                    details += f"• {suggestion}\n"
                details += "\n"

            if result.validation_errors:
                details += "Validation Errors:\n"
                for error in result.validation_errors:
                    details += f"• {error}\n"

            self.details_text.delete('1.0', tk.END)
            self.details_text.insert('1.0', details)

    def open_output_directory(self):
        """Open the output directory in file explorer"""
        output_dir = self.output_dir_var.get()
        if output_dir and os.path.exists(output_dir):
            if sys.platform == "win32":
                os.startfile(output_dir)
            elif sys.platform == "darwin":
                os.system(f"open '{output_dir}'")
            else:
                os.system(f"xdg-open '{output_dir}'")
        else:
            messagebox.showwarning("Warning", "Output directory not found or not set.")

    def test_dashboard(self):
        """Test the generated dashboard"""
        output_dir = self.output_dir_var.get()
        if not output_dir or not os.path.exists(output_dir):
            messagebox.showwarning("Warning", "Output directory not found.")
            return

        server_file = os.path.join(output_dir, 'server.py')
        if not os.path.exists(server_file):
            messagebox.showerror("Error", "server.py not found in output directory.")
            return

        # Ask user if they want to start the test server
        if messagebox.askyesno("Test Dashboard",
                              "This will start the dashboard server for testing.\n\n"
                              "Make sure you have the required dependencies installed.\n\n"
                              "Continue?"):
            try:
                # Start the server in a new process
                import subprocess
                subprocess.Popen([sys.executable, server_file], cwd=output_dir)

                messagebox.showinfo("Server Started",
                                  "Dashboard server started!\n\n"
                                  "The dashboard should be available at:\n"
                                  "http://localhost:5000\n\n"
                                  "Check the terminal for server output.")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to start server: {str(e)}")

    def copy_generation_report(self):
        """Copy generation report to clipboard"""
        report_content = self.generation_results.get('1.0', tk.END)
        self.root.clipboard_clear()
        self.root.clipboard_append(report_content)
        messagebox.showinfo("Copied", "Generation report copied to clipboard.")

    def refresh_logs(self):
        """Refresh the log display"""
        try:
            if os.path.exists('dashboard_customizer.log'):
                with open('dashboard_customizer.log', 'r', encoding='utf-8') as f:
                    log_content = f.read()

                self.log_text.delete('1.0', tk.END)
                self.log_text.insert('1.0', log_content)
                self.log_text.see(tk.END)
            else:
                self.log_text.delete('1.0', tk.END)
                self.log_text.insert('1.0', "No log file found.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to refresh logs: {str(e)}")

    def clear_logs(self):
        """Clear the log display and file"""
        if messagebox.askyesno("Clear Logs", "Clear all logs? This action cannot be undone."):
            self.log_text.delete('1.0', tk.END)
            try:
                if os.path.exists('dashboard_customizer.log'):
                    open('dashboard_customizer.log', 'w').close()
            except Exception as e:
                messagebox.showerror("Error", f"Failed to clear log file: {str(e)}")

    def save_logs(self):
        """Save logs to a file"""
        file_path = filedialog.asksaveasfilename(
            title="Save Logs",
            defaultextension=".log",
            filetypes=[("Log files", "*.log"), ("Text files", "*.txt"), ("All files", "*.*")]
        )

        if file_path:
            try:
                log_content = self.log_text.get('1.0', tk.END)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(log_content)

                messagebox.showinfo("Success", f"Logs saved to {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save logs: {str(e)}")

    def change_log_level(self, event=None):
        """Change the logging level"""
        level = self.log_level_var.get()
        numeric_level = getattr(logging, level, logging.INFO)
        logger.setLevel(numeric_level)
        logger.info(f"Log level changed to {level}")

    def run(self):
        """Run the GUI application"""
        try:
            # Load initial logs
            self.refresh_logs()

            # Start the main loop
            self.root.mainloop()

        except Exception as e:
            logger.error(f"Application error: {str(e)}")
            messagebox.showerror("Application Error", f"An error occurred: {str(e)}")

def main():
    """Main entry point"""
    try:
        # Create and run the GUI application
        app = DashboardCustomizerGUI()
        app.run()

    except Exception as e:
        print(f"Failed to start application: {str(e)}")
        logger.error(f"Failed to start application: {str(e)}")

if __name__ == "__main__":
    main()