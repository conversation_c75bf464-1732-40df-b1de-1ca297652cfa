#!/usr/bin/env python3
"""
Test script to verify the app can start properly.
Run this before deploying to catch any startup issues.
"""
import os
import sys
import time
import requests
import subprocess
from threading import Thread

def test_app_startup():
    """Test that the app starts and responds to health checks"""
    print("🧪 Testing app startup...")
    
    # Set test environment
    os.environ['FLASK_ENV'] = 'development'
    os.environ['PORT'] = '8001'  # Use different port for testing
    
    # Start the server in a subprocess
    try:
        print("🚀 Starting server...")
        process = subprocess.Popen([
            sys.executable, 'server.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait a moment for server to start
        time.sleep(5)

        # Check if process is still running
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            print(f"❌ Server exited early:")
            print(f"STDOUT: {stdout.decode()}")
            print(f"STDERR: {stderr.decode()}")
            return
        
        # Test health endpoints
        try:
            print("🏥 Testing health endpoints...")
            
            # Test simple health check
            response = requests.get('http://localhost:8001/healthz', timeout=5)
            if response.status_code == 200:
                print("✅ /healthz endpoint working")
            else:
                print(f"❌ /healthz returned {response.status_code}")
                
            # Test detailed health check
            response = requests.get('http://localhost:8001/health', timeout=10)
            if response.status_code in [200, 503]:  # 503 is ok if env vars missing
                print("✅ /health endpoint working")
                data = response.json()
                print(f"   Status: {data.get('status', 'unknown')}")
            else:
                print(f"❌ /health returned {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Health check failed: {e}")
            
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        
    finally:
        # Clean up
        if 'process' in locals():
            process.terminate()
            process.wait()
        print("🧹 Cleanup complete")

if __name__ == '__main__':
    test_app_startup()
